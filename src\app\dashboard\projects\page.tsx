import { requireAuth } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Plus, Calendar, Users, MoreHorizontal } from 'lucide-react'
import Link from 'next/link'

export default async function ProjectsPage() {
  const user = await requireAuth()

  // Mock projects data
  const projects = [
    {
      id: 1,
      title: "تطوير موقع الشركة",
      description: "إنشاء موقع إلكتروني جديد للشركة مع لوحة تحكم",
      status: "نشط",
      priority: "عالية",
      dueDate: "2024-02-15",
      team: ["أحمد", "فاطمة", "محمد"],
      progress: 65
    },
    {
      id: 2,
      title: "تطبيق الهاتف المحمول",
      description: "تطوير تطبيق للهواتف الذكية لإدارة المهام",
      status: "قيد المراجعة",
      priority: "متوسطة",
      dueDate: "2024-03-01",
      team: ["سارة", "علي"],
      progress: 40
    },
    {
      id: 3,
      title: "نظام إدارة المخزون",
      description: "تطوير نظام شامل لإدارة المخزون والمبيعات",
      status: "مكتمل",
      priority: "عالية",
      dueDate: "2024-01-20",
      team: ["خالد", "نور", "يوسف", "مريم"],
      progress: 100
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "نشط":
        return "bg-green-100 text-green-800"
      case "قيد المراجعة":
        return "bg-yellow-100 text-yellow-800"
      case "مكتمل":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "عالية":
        return "bg-red-100 text-red-800"
      case "متوسطة":
        return "bg-orange-100 text-orange-800"
      case "منخفضة":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <DashboardLayout user={user}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold">مشاريعي</h2>
            <p className="text-muted-foreground mt-2">
              إدارة ومتابعة جميع مشاريعك
            </p>
          </div>
          <Button asChild>
            <Link href="/dashboard/projects/new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              مشروع جديد
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المشاريع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{projects.length}</div>
              <p className="text-xs text-muted-foreground">
                +2 من الشهر الماضي
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المشاريع النشطة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {projects.filter(p => p.status === "نشط").length}
              </div>
              <p className="text-xs text-muted-foreground">
                +1 من الأسبوع الماضي
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المشاريع المكتملة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {projects.filter(p => p.status === "مكتمل").length}
              </div>
              <p className="text-xs text-muted-foreground">
                +1 من الشهر الماضي
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">متوسط التقدم</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(projects.reduce((acc, p) => acc + p.progress, 0) / projects.length)}%
              </div>
              <p className="text-xs text-muted-foreground">
                +5% من الأسبوع الماضي
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Projects List */}
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">المشاريع الحالية</h3>
          <div className="grid gap-4">
            {projects.map((project) => (
              <Card key={project.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">{project.title}</CardTitle>
                      <CardDescription>{project.description}</CardDescription>
                    </div>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Status and Priority */}
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(project.status)}>
                        {project.status}
                      </Badge>
                      <Badge variant="outline" className={getPriorityColor(project.priority)}>
                        أولوية {project.priority}
                      </Badge>
                    </div>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>التقدم</span>
                        <span>{project.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${project.progress}%` }}
                        />
                      </div>
                    </div>

                    {/* Project Details */}
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>موعد التسليم: {project.dueDate}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span>{project.team.length} أعضاء</span>
                      </div>
                    </div>

                    {/* Team Members */}
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">الفريق:</span>
                      <div className="flex gap-1">
                        {project.team.map((member, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {member}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
