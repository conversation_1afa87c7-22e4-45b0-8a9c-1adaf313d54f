'use client'

import { useState, useEffect } from 'react'
import { UserManagement } from './UserManagement'

type Profile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: 'admin' | 'user'
  created_at: string
  updated_at: string
}

export function UserManagementPage() {
  const [users, setUsers] = useState<Profile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const fetchUsers = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true)
      setError('')

      const response = await fetch('/api/admin/users', {
        cache: 'no-store' // Ensure fresh data
      })
      const result = await response.json()

      if (!response.ok) {
        setError(result.error || 'خطأ في جلب البيانات')
        return
      }

      setUsers(result.users || [])
    } catch (err) {
      setError('حدث خطأ في الاتصال')
    } finally {
      if (showLoading) setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="h-8 bg-muted rounded animate-pulse w-48"></div>
          <div className="h-4 bg-muted rounded animate-pulse w-64"></div>
        </div>
        <div className="h-96 bg-muted rounded animate-pulse"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold">إدارة المستخدمين</h2>
          <p className="text-muted-foreground mt-2">
            إدارة ومتابعة جميع مستخدمي النظام
          </p>
        </div>
        <div className="text-center text-destructive bg-destructive/10 p-8 rounded-lg">
          <p className="text-lg font-medium mb-4">{error}</p>
          <button 
            onClick={() => fetchUsers()} 
            className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold">إدارة المستخدمين</h2>
        <p className="text-muted-foreground mt-2">
          إدارة ومتابعة جميع مستخدمي النظام
        </p>
      </div>

      <UserManagement users={users} onUserChange={() => fetchUsers(false)} />
    </div>
  )
}
