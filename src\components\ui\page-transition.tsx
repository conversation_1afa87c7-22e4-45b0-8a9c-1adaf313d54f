'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { QuickLoading } from './loading'

export function PageTransition({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    setIsLoading(true)
    const timer = setTimeout(() => setIsLoading(false), 100)
    return () => clearTimeout(timer)
  }, [pathname])

  if (isLoading) {
    return <QuickLoading />
  }

  return <>{children}</>
}
