import { requireAdmin } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Plus, Users, UserCheck, UserX, MoreHorizontal, Mail, Phone } from 'lucide-react'
import Link from 'next/link'

export default async function AdminUsersPage() {
  const profile = await requireAdmin()

  // Mock users data
  const users = [
    {
      id: 1,
      name: "أحمد محمد علي",
      email: "<EMAIL>",
      phone: "+966501234567",
      role: "مطور",
      status: "نشط",
      avatar: "",
      joinDate: "2024-01-15",
      lastLogin: "2024-01-19",
      projectsCount: 3,
      tasksCount: 12
    },
    {
      id: 2,
      name: "فاطمة أحمد",
      email: "<EMAIL>",
      phone: "+966507654321",
      role: "مصممة",
      status: "نشط",
      avatar: "",
      joinDate: "2024-01-10",
      lastLogin: "2024-01-19",
      projectsCount: 2,
      tasksCount: 8
    },
    {
      id: 3,
      name: "محمد خالد",
      email: "<EMAIL>",
      phone: "+966509876543",
      role: "مدير مشروع",
      status: "نشط",
      avatar: "",
      joinDate: "2024-01-05",
      lastLogin: "2024-01-18",
      projectsCount: 5,
      tasksCount: 15
    },
    {
      id: 4,
      name: "سارة علي",
      email: "<EMAIL>",
      phone: "+966502468135",
      role: "محللة",
      status: "غير نشط",
      avatar: "",
      joinDate: "2024-01-12",
      lastLogin: "2024-01-16",
      projectsCount: 1,
      tasksCount: 4
    },
    {
      id: 5,
      name: "علي حسن",
      email: "<EMAIL>",
      phone: "+966503691472",
      role: "مطور",
      status: "نشط",
      avatar: "",
      joinDate: "2024-01-08",
      lastLogin: "2024-01-19",
      projectsCount: 2,
      tasksCount: 9
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "نشط":
        return "bg-green-100 text-green-800"
      case "غير نشط":
        return "bg-red-100 text-red-800"
      case "معلق":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case "مدير مشروع":
        return "bg-purple-100 text-purple-800"
      case "مطور":
        return "bg-blue-100 text-blue-800"
      case "مصممة":
        return "bg-pink-100 text-pink-800"
      case "محللة":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const activeUsers = users.filter(user => user.status === "نشط")
  const inactiveUsers = users.filter(user => user.status !== "نشط")

  return (
    <DashboardLayout user={{
      name: profile.full_name,
      email: profile.email,
      role: 'admin'
    }}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold">إدارة المستخدمين</h2>
            <p className="text-muted-foreground mt-2">
              إدارة ومتابعة جميع مستخدمي النظام
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/users/new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              إضافة مستخدم
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المستخدمين</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
              <p className="text-xs text-muted-foreground">
                +2 من الشهر الماضي
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المستخدمون النشطون</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeUsers.length}</div>
              <p className="text-xs text-muted-foreground">
                +1 من الأسبوع الماضي
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المستخدمون غير النشطين</CardTitle>
              <UserX className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inactiveUsers.length}</div>
              <p className="text-xs text-muted-foreground">
                لا تغيير
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">متوسط المشاريع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(users.reduce((acc, user) => acc + user.projectsCount, 0) / users.length)}
              </div>
              <p className="text-xs text-muted-foreground">
                مشروع لكل مستخدم
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Users List */}
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">قائمة المستخدمين</h3>
          <div className="grid gap-4">
            {users.map((user) => (
              <Card key={user.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={user.avatar} alt={user.name} />
                        <AvatarFallback>
                          {user.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-2">
                        <div>
                          <h4 className="font-semibold text-lg">{user.name}</h4>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Mail className="h-4 w-4" />
                              <span>{user.email}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Phone className="h-4 w-4" />
                              <span>{user.phone}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(user.status)}>
                            {user.status}
                          </Badge>
                          <Badge variant="outline" className={getRoleColor(user.role)}>
                            {user.role}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">تاريخ الانضمام:</span>
                            <div className="font-medium">{user.joinDate}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">آخر دخول:</span>
                            <div className="font-medium">{user.lastLogin}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">المشاريع:</span>
                            <div className="font-medium">{user.projectsCount}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">المهام:</span>
                            <div className="font-medium">{user.tasksCount}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        تعديل
                      </Button>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
