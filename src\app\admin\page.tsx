import { requireAdmin } from '@/lib/auth'
import { AdminDashboard } from '@/components/admin/AdminDashboard'
import { DashboardLayout } from '@/components/layout/dashboard-layout'

export default async function AdminPage() {
  const profile = await requireAdmin()

  return (
    <DashboardLayout user={{
      name: profile.full_name,
      email: profile.email,
      role: 'admin'
    }}>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold">مرحباً بك في لوحة الإدارة</h2>
          <p className="text-muted-foreground mt-2">
            إدارة النظام والمستخدمين
          </p>
        </div>

        <AdminDashboard />
      </div>
    </DashboardLayout>
  )
}
