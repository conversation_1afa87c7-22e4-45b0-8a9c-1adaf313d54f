import { requireAuth } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Plus, Calendar, Clock, Flag, MoreHorizontal } from 'lucide-react'
import Link from 'next/link'

export default async function TasksPage() {
  const user = await requireAuth()

  // Mock tasks data
  const tasks = [
    {
      id: 1,
      title: "مراجعة التصميم الجديد",
      description: "مراجعة وتقييم التصميم المقترح للصفحة الرئيسية",
      status: "قيد التنفيذ",
      priority: "عالية",
      dueDate: "2024-01-20",
      project: "تطوير موقع الشركة",
      completed: false,
      assignee: "أحمد محمد"
    },
    {
      id: 2,
      title: "كتابة الوثائق التقنية",
      description: "إعداد الوثائق التقنية للمطورين",
      status: "جديدة",
      priority: "متوسطة",
      dueDate: "2024-01-25",
      project: "تطبيق الهاتف المحمول",
      completed: false,
      assignee: "فاطمة علي"
    },
    {
      id: 3,
      title: "اختبار الوحدات",
      description: "إجراء اختبارات شاملة للوحدات المطورة",
      status: "مكتملة",
      priority: "عالية",
      dueDate: "2024-01-15",
      project: "نظام إدارة المخزون",
      completed: true,
      assignee: "محمد خالد"
    },
    {
      id: 4,
      title: "تحديث قاعدة البيانات",
      description: "تحديث هيكل قاعدة البيانات وإضافة الجداول الجديدة",
      status: "قيد المراجعة",
      priority: "عالية",
      dueDate: "2024-01-22",
      project: "تطوير موقع الشركة",
      completed: false,
      assignee: "سارة أحمد"
    },
    {
      id: 5,
      title: "إعداد التقرير الشهري",
      description: "إعداد تقرير شامل عن إنجازات الشهر",
      status: "جديدة",
      priority: "منخفضة",
      dueDate: "2024-01-30",
      project: "إدارة عامة",
      completed: false,
      assignee: "علي حسن"
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "جديدة":
        return "bg-blue-100 text-blue-800"
      case "قيد التنفيذ":
        return "bg-yellow-100 text-yellow-800"
      case "قيد المراجعة":
        return "bg-orange-100 text-orange-800"
      case "مكتملة":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "عالية":
        return "text-red-600"
      case "متوسطة":
        return "text-orange-600"
      case "منخفضة":
        return "text-green-600"
      default:
        return "text-gray-600"
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "عالية":
        return <Flag className="h-4 w-4 text-red-600" />
      case "متوسطة":
        return <Flag className="h-4 w-4 text-orange-600" />
      case "منخفضة":
        return <Flag className="h-4 w-4 text-green-600" />
      default:
        return <Flag className="h-4 w-4 text-gray-600" />
    }
  }

  const activeTasks = tasks.filter(task => !task.completed)
  const completedTasks = tasks.filter(task => task.completed)

  return (
    <DashboardLayout user={user}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold">المهام</h2>
            <p className="text-muted-foreground mt-2">
              إدارة ومتابعة جميع مهامك اليومية
            </p>
          </div>
          <Button asChild>
            <Link href="/dashboard/tasks/new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              مهمة جديدة
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المهام</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tasks.length}</div>
              <p className="text-xs text-muted-foreground">
                +3 من الأسبوع الماضي
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المهام النشطة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeTasks.length}</div>
              <p className="text-xs text-muted-foreground">
                +2 من الأمس
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المهام المكتملة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{completedTasks.length}</div>
              <p className="text-xs text-muted-foreground">
                +1 اليوم
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">معدل الإنجاز</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round((completedTasks.length / tasks.length) * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">
                +10% من الأسبوع الماضي
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Active Tasks */}
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">المهام النشطة</h3>
          <div className="space-y-3">
            {activeTasks.map((task) => (
              <Card key={task.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    <Checkbox 
                      id={`task-${task.id}`}
                      checked={task.completed}
                      className="mt-1"
                    />
                    <div className="flex-1 space-y-2">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-medium">{task.title}</h4>
                          <p className="text-sm text-muted-foreground">{task.description}</p>
                        </div>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm">
                        <Badge className={getStatusColor(task.status)}>
                          {task.status}
                        </Badge>
                        <div className="flex items-center gap-1">
                          {getPriorityIcon(task.priority)}
                          <span className={getPriorityColor(task.priority)}>
                            {task.priority}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          <span>{task.dueDate}</span>
                        </div>
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <span>المشروع: {task.project}</span>
                        </div>
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        مُكلف إلى: {task.assignee}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Completed Tasks */}
        {completedTasks.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">المهام المكتملة</h3>
            <div className="space-y-3">
              {completedTasks.map((task) => (
                <Card key={task.id} className="opacity-75">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <Checkbox 
                        id={`task-${task.id}`}
                        checked={task.completed}
                        className="mt-1"
                      />
                      <div className="flex-1 space-y-2">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium line-through">{task.title}</h4>
                            <p className="text-sm text-muted-foreground">{task.description}</p>
                          </div>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm">
                          <Badge className={getStatusColor(task.status)}>
                            {task.status}
                          </Badge>
                          <div className="flex items-center gap-1 text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span>{task.dueDate}</span>
                          </div>
                          <div className="flex items-center gap-1 text-muted-foreground">
                            <span>المشروع: {task.project}</span>
                          </div>
                        </div>
                        
                        <div className="text-sm text-muted-foreground">
                          مُكلف إلى: {task.assignee}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
