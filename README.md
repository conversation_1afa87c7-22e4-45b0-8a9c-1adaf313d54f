# سحابة المدينة (Sahabat Al-Madinah)

نظام إدارة العمل المخصص باللغة العربية مع دعم RTL كامل.

## المميزات

- 🌐 **دعم كامل للغة العربية**: واجهة مستخدم باللغة العربية مع دعم RTL
- 🎨 **تصميم عصري**: استخدام shadcn/ui مع ألوان أبيض وأزرق داكن
- 🔐 **نظام مصادقة متقدم**: باستخدام Supabase مع إدارة الأدوار
- 👥 **إدارة المستخدمين**: المدير يمكنه إضافة وإدارة المستخدمين
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة
- 🔒 **حماية الصفحات**: حماية تلقائية للصفحات حسب الأدوار

## التقنيات المستخدمة

- **Next.js 15** - إطار عمل React
- **TypeScript** - للكتابة الآمنة
- **Tailwind CSS** - للتصميم
- **shadcn/ui** - مكونات واجهة المستخدم
- **Supabase** - قاعدة البيانات والمصادقة
- **IBM Plex Arabic** - الخط العربي

## الإعداد والتشغيل

### 1. تثبيت المتطلبات

```bash
npm install
```

### 2. إعداد متغيرات البيئة

أنشئ ملف `.env.local` وأضف:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. إعداد قاعدة البيانات

قم بتشغيل الـ migration في Supabase:

```sql
-- نسخ محتوى ملف supabase/migrations/001_initial_schema.sql
-- وتشغيله في SQL Editor في Supabase Dashboard
```

### 4. إنشاء أول مدير

بعد تشغيل الـ migration، قم بإنشاء مستخدم في Supabase Auth، ثم قم بتحديث دوره:

```sql
UPDATE profiles
SET role = 'admin'
WHERE email = '<EMAIL>';
```

### 5. تشغيل المشروع

```bash
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح.

## هيكل المشروع

```
src/
├── app/                    # صفحات Next.js
│   ├── admin/             # صفحات المدير
│   ├── dashboard/         # صفحات المستخدم العادي
│   ├── login/             # صفحة تسجيل الدخول
│   └── unauthorized/      # صفحة عدم التصريح
├── components/            # المكونات
│   ├── admin/            # مكونات المدير
│   ├── providers/        # مزودي السياق
│   └── ui/               # مكونات shadcn/ui
├── hooks/                # React Hooks
├── lib/                  # المكتبات والأدوات
│   └── supabase/        # إعداد Supabase
└── middleware.ts         # حماية الصفحات
```

## الأدوار والصلاحيات

### المدير (Admin)
- إدارة جميع المستخدمين
- إضافة مستخدمين جدد
- تعديل أدوار المستخدمين
- حذف المستخدمين
- الوصول لجميع أجزاء النظام

### المستخدم العادي (User)
- الوصول للوحة التحكم الشخصية
- عرض المهام والمشاريع الخاصة به
- تحديث الملف الشخصي

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التغييرات مع الالتزام بالمعايير
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
