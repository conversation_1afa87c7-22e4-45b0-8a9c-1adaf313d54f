"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Home,
  Users,
  Settings,
  FileText,
  BarChart3,
  Calendar,
  MessageSquare,
  Bell,
  Search,
  Plus,
  ChevronRight,
  User,
  LogOut,
  Cloud,
} from "lucide-react"

import { cn } from "@/lib/utils"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { UserAvatar } from "@/components/ui/user-avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { LogoutButton } from "@/components/ui/logout-button"

// Navigation data for different user roles
const adminNavigation = [
  {
    title: "الرئيسية",
    url: "/admin",
    icon: Home,
  },
  {
    title: "إدارة المستخدمين",
    url: "/admin/users",
    icon: Users,
    items: [
      {
        title: "جميع المستخدمين",
        url: "/admin/users",
      },
      {
        title: "إضافة مستخدم",
        url: "/admin/users/new",
      },
      {
        title: "الأدوار والصلاحيات",
        url: "/admin/users/roles",
      },
    ],
  },
  {
    title: "التقارير",
    url: "/admin/reports",
    icon: BarChart3,
    items: [
      {
        title: "تقارير النشاط",
        url: "/admin/reports/activity",
      },
      {
        title: "تقارير المستخدمين",
        url: "/admin/reports/users",
      },
      {
        title: "إحصائيات النظام",
        url: "/admin/reports/system",
      },
    ],
  },
  {
    title: "المشاريع",
    url: "/admin/projects",
    icon: FileText,
    items: [
      {
        title: "جميع المشاريع",
        url: "/admin/projects",
      },
      {
        title: "مشروع جديد",
        url: "/admin/projects/new",
      },
      {
        title: "المشاريع المؤرشفة",
        url: "/admin/projects/archived",
      },
    ],
  },
  {
    title: "التقويم",
    url: "/admin/calendar",
    icon: Calendar,
  },
  {
    title: "الرسائل",
    url: "/admin/messages",
    icon: MessageSquare,
    badge: "5",
  },
  {
    title: "الإعدادات",
    url: "/admin/settings",
    icon: Settings,
    items: [
      {
        title: "إعدادات النظام",
        url: "/admin/settings/system",
      },
      {
        title: "إعدادات الأمان",
        url: "/admin/settings/security",
      },
      {
        title: "النسخ الاحتياطي",
        url: "/admin/settings/backup",
      },
    ],
  },
]

const userNavigation = [
  {
    title: "الرئيسية",
    url: "/dashboard",
    icon: Home,
  },
  {
    title: "مشاريعي",
    url: "/dashboard/projects",
    icon: FileText,
    items: [
      {
        title: "المشاريع النشطة",
        url: "/dashboard/projects/active",
      },
      {
        title: "المشاريع المكتملة",
        url: "/dashboard/projects/completed",
      },
      {
        title: "مشروع جديد",
        url: "/dashboard/projects/new",
      },
    ],
  },
  {
    title: "المهام",
    url: "/dashboard/tasks",
    icon: FileText,
    badge: "12",
  },
  {
    title: "التقويم",
    url: "/dashboard/calendar",
    icon: Calendar,
  },
  {
    title: "الرسائل",
    url: "/dashboard/messages",
    icon: MessageSquare,
    badge: "3",
  },
  {
    title: "الملف الشخصي",
    url: "/dashboard/profile",
    icon: User,
  },
]

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  user?: {
    name?: string
    email?: string
    avatar?: string
    role?: string
  }
}

export function AppSidebar({ user, ...props }: AppSidebarProps) {
  const pathname = usePathname()
  const isAdmin = pathname.startsWith('/admin')
  const navigation = isAdmin ? adminNavigation : userNavigation

  return (
    <Sidebar collapsible="icon" side="right" {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-2 py-2 min-h-[3rem] group-data-[collapsible=icon]:justify-center">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground shrink-0">
            <Cloud className="size-4" />
          </div>
          <div className="grid flex-1 text-right text-sm leading-tight group-data-[collapsible=icon]:hidden">
            <span className="truncate font-semibold">سحابة المدينة</span>
            <span className="truncate text-xs text-muted-foreground">
              {isAdmin ? "لوحة الإدارة" : "نظام إدارة العمل"}
            </span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>التنقل الرئيسي</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="group-data-[collapsible=icon]:items-center">
              {navigation.map((item) => {
                const isActive = pathname === item.url || pathname.startsWith(item.url + '/')
                
                if (item.items) {
                  return (
                    <Collapsible key={item.title} asChild defaultOpen={isActive}>
                      <SidebarMenuItem>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton
                            isActive={isActive}
                            className="w-full justify-between"
                          >
                            <div className="flex items-center gap-2">
                              <item.icon className="size-4" />
                              <span className="flex-1 text-right">{item.title}</span>
                              {item.badge && (
                                <span className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                                  {item.badge}
                                </span>
                              )}
                            </div>
                            <ChevronRight className="size-4 transition-transform group-data-[state=open]:rotate-90" />
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {item.items.map((subItem) => (
                              <SidebarMenuSubItem key={subItem.title}>
                                <SidebarMenuSubButton
                                  asChild
                                  isActive={pathname === subItem.url}
                                >
                                  <Link href={subItem.url} className="text-right">
                                    {subItem.title}
                                  </Link>
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            ))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </SidebarMenuItem>
                    </Collapsible>
                  )
                }

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild isActive={isActive}>
                      <Link href={item.url} className="flex items-center gap-2">
                        <item.icon className="size-4" />
                        <span className="flex-1 text-right">{item.title}</span>
                        {item.badge && (
                          <span className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                            {item.badge}
                          </span>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>إجراءات سريعة</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="group-data-[collapsible=icon]:items-center">
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <Plus className="size-4" />
                    <span className="text-right">إضافة جديد</span>
                  </Button>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <Search className="size-4" />
                    <span className="text-right">البحث</span>
                  </Button>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <UserAvatar
                    src={user?.avatar}
                    alt={user?.name}
                    name={user?.name}
                    size="md"
                    className="rounded-lg"
                    fallbackClassName="rounded-lg"
                  />
                  <div className="grid flex-1 text-right text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {user?.name || user?.email || 'المستخدم'}
                    </span>
                    <span className="truncate text-xs">
                      {isAdmin ? 'مدير النظام' : 'مستخدم'}
                    </span>
                  </div>
                  <ChevronRight className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-right text-sm">
                    <UserAvatar
                      src={user?.avatar}
                      alt={user?.name}
                      name={user?.name}
                      size="md"
                      className="rounded-lg"
                      fallbackClassName="rounded-lg"
                    />
                    <div className="grid flex-1 text-right text-sm leading-tight">
                      <span className="truncate font-semibold">
                        {user?.name || user?.email || 'المستخدم'}
                      </span>
                      <span className="truncate text-xs">
                        {user?.email}
                      </span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/profile" className="flex items-center gap-2">
                    <User className="size-4" />
                    الملف الشخصي
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings" className="flex items-center gap-2">
                    <Settings className="size-4" />
                    الإعدادات
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <LogoutButton className="w-full justify-start gap-2 p-0">
                    <LogOut className="size-4" />
                    تسجيل الخروج
                  </LogoutButton>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
