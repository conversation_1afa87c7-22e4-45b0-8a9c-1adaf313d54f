'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Button } from './button'
import { LogOut } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LogoutButtonProps {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  className?: string
  showIcon?: boolean
  children?: React.ReactNode
}

export function LogoutButton({ 
  variant = 'outline', 
  size = 'sm', 
  className,
  showIcon = true,
  children 
}: LogoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { signOut } = useAuth()

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleSignOut}
      disabled={isLoading}
      className={cn(className)}
    >
      {showIcon && <LogOut className="h-4 w-4 ml-2" />}
      {children || (isLoading ? 'جاري تسجيل الخروج...' : 'تسجيل الخروج')}
    </Button>
  )
}
