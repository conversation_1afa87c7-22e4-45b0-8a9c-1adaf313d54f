'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Edit, Trash2, Mail, Calendar, Eye, User } from 'lucide-react'
import Link from 'next/link'

type Profile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: 'admin' | 'user'
  created_at: string
  updated_at: string
}

interface UserManagementProps {
  users: Profile[]
  onUserChange?: () => void
}

export function UserManagement({ users: initialUsers, onUserChange }: UserManagementProps) {
  const [users, setUsers] = useState(initialUsers)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // Update local users state when initialUsers prop changes
  useEffect(() => {
    setUsers(initialUsers)
  }, [initialUsers])
  
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    full_name: '',
    phone: '',
    role: 'user' as 'admin' | 'user'
  })



  const handleAddUser = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      // Call API route to create user (since we need service role key)
      const response = await fetch('/api/admin/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: newUser.email,
          password: newUser.password,
          full_name: newUser.full_name,
          phone: newUser.phone,
          role: newUser.role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في إنشاء المستخدم: ' + result.error)
        return
      }

      // Reset form and close dialog
      setNewUser({ email: '', password: '', full_name: '', phone: '', role: 'user' })
      setIsAddDialogOpen(false)
      setSuccess('تم إضافة المستخدم بنجاح')

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)

      // Trigger immediate refresh
      if (onUserChange) {
        onUserChange()
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const handleEditUser = async () => {
    if (!editingUser) return

    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/admin/update-user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: editingUser.id,
          full_name: editingUser.full_name,
          phone: editingUser.phone,
          role: editingUser.role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في تحديث المستخدم: ' + result.error)
        return
      }

      setIsEditDialogOpen(false)
      setEditingUser(null)
      setSuccess('تم تحديث المستخدم بنجاح')

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)

      // Trigger immediate refresh
      if (onUserChange) {
        onUserChange()
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteUser = async (userId: string) => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      // Call API route to delete user
      const response = await fetch('/api/admin/delete-user', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في حذف المستخدم: ' + result.error)
        return
      }

      setSuccess('تم حذف المستخدم بنجاح')

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)

      // Trigger immediate refresh
      if (onUserChange) {
        onUserChange()
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>إدارة المستخدمين</CardTitle>
            <CardDescription>
              عرض وإدارة جميع مستخدمي النظام
            </CardDescription>
          </div>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 ml-2" />
                إضافة مستخدم
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] text-right" dir="rtl">
              <DialogHeader className="text-right">
                <DialogTitle className="text-right">إضافة مستخدم جديد</DialogTitle>
                <DialogDescription className="text-right">
                  أدخل بيانات المستخدم الجديد
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                {error && (
                  <div className="text-sm text-destructive bg-destructive/10 p-2 rounded text-right" dir="rtl">
                    {error}
                  </div>
                )}

                {success && (
                  <div className="text-sm text-green-800 bg-green-100 p-2 rounded text-right" dir="rtl">
                    {success}
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-right block">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    placeholder="أدخل البريد الإلكتروني"
                    className="text-right"
                    dir="rtl"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-right block">كلمة المرور</Label>
                  <Input
                    id="password"
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                    placeholder="أدخل كلمة المرور"
                    className="text-right"
                    dir="rtl"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="full_name" className="text-right block">الاسم الكامل</Label>
                  <Input
                    id="full_name"
                    value={newUser.full_name}
                    onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
                    placeholder="أدخل الاسم الكامل"
                    className="text-right"
                    dir="rtl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-right block">رقم الهاتف</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={newUser.phone}
                    onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
                    placeholder="أدخل رقم الهاتف"
                    className="text-right"
                    dir="rtl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role" className="text-right block">الدور</Label>
                  <Select value={newUser.role} onValueChange={(value: 'admin' | 'user') => setNewUser({ ...newUser, role: value })} dir="rtl">
                    <SelectTrigger className="text-right" dir="rtl">
                      <SelectValue placeholder="اختر الدور" />
                    </SelectTrigger>
                    <SelectContent className="text-right" dir="rtl">
                      <SelectItem value="user" className="text-right">مستخدم عادي</SelectItem>
                      <SelectItem value="admin" className="text-right">مدير</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex justify-start gap-2 mt-6" dir="rtl">
                <Button onClick={handleAddUser} disabled={loading}>
                  {loading ? 'جاري الإضافة...' : 'إضافة'}
                </Button>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  إلغاء
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      {/* Global success/error messages */}
      {(success || error) && (
        <div className="px-6 pb-4" dir="rtl">
          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md mb-2 text-right">
              {error}
            </div>
          )}
          {success && (
            <div className="text-sm text-green-800 bg-green-100 p-3 rounded-md text-right">
              {success}
            </div>
          )}
        </div>
      )}

      <CardContent>
        {loading && (
          <div className="text-center py-4" dir="rtl">
            <div className="inline-flex items-center gap-2 text-muted-foreground">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
              جاري تحديث البيانات...
            </div>
          </div>
        )}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">الاسم</TableHead>
              <TableHead className="text-right">البريد الإلكتروني</TableHead>
              <TableHead className="text-right">رقم الهاتف</TableHead>
              <TableHead className="text-right">الدور</TableHead>
              <TableHead className="text-right">تاريخ الإنشاء</TableHead>
              <TableHead className="text-right">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id} className="hover:bg-muted/50 cursor-pointer">
                <TableCell className="text-right">
                  {user.full_name || 'غير محدد'}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    {user.email}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  {user.phone || 'غير محدد'}
                </TableCell>
                <TableCell className="text-right">
                  <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                    {user.role === 'admin' ? 'مدير' : 'مستخدم'}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    {formatDate(user.created_at)}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2">
                    <Link href={`/admin/users/${user.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>

                    <Dialog open={isEditDialogOpen && editingUser?.id === user.id} onOpenChange={(open) => {
                      setIsEditDialogOpen(open)
                      if (!open) setEditingUser(null)
                    }}>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingUser(user)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px] text-right" dir="rtl">
                        <DialogHeader className="text-right">
                          <DialogTitle className="text-right">تعديل المستخدم</DialogTitle>
                          <DialogDescription className="text-right">
                            تعديل بيانات المستخدم
                          </DialogDescription>
                        </DialogHeader>
                        
                        {editingUser && (
                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="edit_full_name" className="text-right block">الاسم الكامل</Label>
                              <Input
                                id="edit_full_name"
                                value={editingUser.full_name || ''}
                                onChange={(e) => setEditingUser({ ...editingUser, full_name: e.target.value })}
                                placeholder="أدخل الاسم الكامل"
                                className="text-right"
                                dir="rtl"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="edit_phone" className="text-right block">رقم الهاتف</Label>
                              <Input
                                id="edit_phone"
                                type="tel"
                                value={editingUser.phone || ''}
                                onChange={(e) => setEditingUser({ ...editingUser, phone: e.target.value })}
                                placeholder="أدخل رقم الهاتف"
                                className="text-right"
                                dir="rtl"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="edit_role" className="text-right block">الدور</Label>
                              <Select value={editingUser.role} onValueChange={(value: 'admin' | 'user') => setEditingUser({ ...editingUser, role: value })} dir="rtl">
                                <SelectTrigger className="text-right" dir="rtl">
                                  <SelectValue placeholder="اختر الدور" />
                                </SelectTrigger>
                                <SelectContent className="text-right" dir="rtl">
                                  <SelectItem value="user" className="text-right">مستخدم عادي</SelectItem>
                                  <SelectItem value="admin" className="text-right">مدير</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}
                        
                        <div className="flex justify-start gap-2 mt-6" dir="rtl">
                          <Button onClick={handleEditUser} disabled={loading}>
                            {loading ? 'جاري التحديث...' : 'تحديث'}
                          </Button>
                          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                            إلغاء
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                    
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="text-right" dir="rtl">
                        <AlertDialogHeader className="text-right">
                          <AlertDialogTitle className="text-right">تأكيد الحذف</AlertDialogTitle>
                          <AlertDialogDescription className="text-right">
                            هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter className="flex-row-reverse gap-2">
                          <AlertDialogAction
                            onClick={() => handleDeleteUser(user.id)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            حذف
                          </AlertDialogAction>
                          <AlertDialogCancel>إلغاء</AlertDialogCancel>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
