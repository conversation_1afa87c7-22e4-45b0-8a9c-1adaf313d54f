import { requireAuth } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DashboardLayout } from '@/components/layout/dashboard-layout'

export default async function DashboardPage() {
  const user = await requireAuth()

  return (
    <DashboardLayout user={user}>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold">مرحباً بك</h2>
          <p className="text-muted-foreground mt-2">
            لوحة التحكم الخاصة بك في نظام سحابة المدينة
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>المهام</CardTitle>
              <CardDescription>
                إدارة مهامك اليومية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">0</p>
              <p className="text-sm text-muted-foreground">مهمة نشطة</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>المشاريع</CardTitle>
              <CardDescription>
                تتبع تقدم مشاريعك
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">0</p>
              <p className="text-sm text-muted-foreground">مشروع جاري</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>التقارير</CardTitle>
              <CardDescription>
                عرض التقارير والإحصائيات
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">0</p>
              <p className="text-sm text-muted-foreground">تقرير جديد</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
