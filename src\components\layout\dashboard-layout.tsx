"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { PanelLeft, Bell, Search } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import { AppSidebar } from "./app-sidebar"

interface DashboardLayoutProps {
  children: React.ReactNode
  user?: {
    name?: string
    email?: string
    avatar?: string
    role?: string
  }
}

// Breadcrumb mapping for Arabic pages
const breadcrumbMap: Record<string, string> = {
  '/dashboard': 'الرئيسية',
  '/dashboard/projects': 'المشاريع',
  '/dashboard/projects/active': 'المشاريع النشطة',
  '/dashboard/projects/completed': 'المشاريع المكتملة',
  '/dashboard/projects/new': 'مشروع جديد',
  '/dashboard/tasks': 'المهام',
  '/dashboard/calendar': 'التقويم',
  '/dashboard/messages': 'الرسائل',
  '/dashboard/profile': 'الملف الشخصي',
  '/dashboard/settings': 'الإعدادات',
  '/admin': 'لوحة الإدارة',
  '/admin/users': 'إدارة المستخدمين',
  '/admin/users/new': 'إضافة مستخدم',
  '/admin/users/roles': 'الأدوار والصلاحيات',
  '/admin/reports': 'التقارير',
  '/admin/reports/activity': 'تقارير النشاط',
  '/admin/reports/users': 'تقارير المستخدمين',
  '/admin/reports/system': 'إحصائيات النظام',
  '/admin/projects': 'المشاريع',
  '/admin/projects/new': 'مشروع جديد',
  '/admin/projects/archived': 'المشاريع المؤرشفة',
  '/admin/calendar': 'التقويم',
  '/admin/messages': 'الرسائل',
  '/admin/settings': 'الإعدادات',
  '/admin/settings/system': 'إعدادات النظام',
  '/admin/settings/security': 'إعدادات الأمان',
  '/admin/settings/backup': 'النسخ الاحتياطي',
}

function generateBreadcrumbs(pathname: string) {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs = []
  
  let currentPath = ''
  for (const segment of segments) {
    currentPath += `/${segment}`
    const title = breadcrumbMap[currentPath] || segment
    breadcrumbs.push({
      title,
      href: currentPath,
      isLast: currentPath === pathname
    })
  }
  
  return breadcrumbs
}

export function DashboardLayout({ children, user }: DashboardLayoutProps) {
  const pathname = usePathname()
  const breadcrumbs = generateBreadcrumbs(pathname)
  const isAdmin = pathname.startsWith('/admin')

  return (
    <SidebarProvider>
      <AppSidebar user={user} />
      <SidebarInset>
        {/* Header */}
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear">
          {/* Header Actions - Moved to Left */}
          <div className="flex items-center gap-2 px-4">
            {/* Search */}
            <div className="relative hidden md:block">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="البحث..."
                className="w-[200px] pr-8 pl-10 lg:w-[300px] text-right"
                dir="rtl"
              />
            </div>

            {/* Notifications */}
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <span className="absolute -top-1 -left-1 h-3 w-3 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
                3
              </span>
              <span className="sr-only">الإشعارات</span>
            </Button>

            {/* Mobile Search */}
            <Button variant="ghost" size="icon" className="md:hidden">
              <Search className="h-4 w-4" />
              <span className="sr-only">البحث</span>
            </Button>
          </div>

          {/* Breadcrumbs and Sidebar Toggle - Moved to Right */}
          <div className="mr-auto flex items-center gap-2 px-4">
            <SidebarTrigger className="-mr-1" />
            <Separator orientation="vertical" className="ml-2 h-4" />

            {/* Breadcrumbs */}
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((breadcrumb, index) => (
                  <React.Fragment key={breadcrumb.href}>
                    <BreadcrumbItem className="hidden md:block">
                      {breadcrumb.isLast ? (
                        <BreadcrumbPage>{breadcrumb.title}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink href={breadcrumb.href}>
                          {breadcrumb.title}
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && (
                      <BreadcrumbSeparator className="hidden md:block" />
                    )}
                  </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
